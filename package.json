{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "rimraf .next && next build", "start": "next start", "lint": "next lint", "init-data": "node scripts/init-sample-data.js", "figma-mcp": "node scripts/start-figma-mcp.js", "db:migrate": "node scripts/supabase-management-migrations.js", "db:import-exercises": "node scripts/import-exercises.js", "db:import-food": "node scripts/import-food-database.js", "db:check-schema": "node scripts/check-supabase-schema.js", "db:generate-types": "node scripts/generate-supabase-types.js", "db:setup": "node scripts/setup-database.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "latest", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/storage-js": "^2.7.3", "@supabase/supabase-js": "^2.49.4", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "canvas-confetti": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "embla-carousel-react": "8.5.1", "framer-motion": "^12.14.0", "input-otp": "1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "openai": "^4.103.0", "react": "^18", "react-circular-progressbar": "^2.2.0", "react-day-picker": "latest", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "dotenv": "^16.4.5", "figma-developer-mcp": "^0.2.2", "postcss": "^8", "rimraf": "^5.0.10", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}