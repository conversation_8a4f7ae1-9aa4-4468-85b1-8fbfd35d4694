"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { SafeClientButton as Button } from "@/components/ui/safe-client-button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  <PERSON><PERSON><PERSON>, 
  Target, 
  TrendingUp, 
  Zap, 
  Calculator,
  Calendar,
  BarChart3,
  Sparkles,
  CheckCircle2,
  AlertTriangle,
  Play,
  Settings
} from 'lucide-react'
import { useAuth } from "@/lib/auth/auth-context"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { RMCalculator } from "./rm-calculator"
import { EnhancedTrainingPersonalization } from "./enhanced-training-personalization"

interface TrainingDashboardProps {
  userExperienceLevel: 'beginner' | 'intermediate' | 'advanced'
  className?: string
}

interface TrainingStats {
  weeklyWorkouts: number
  targetWorkouts: number
  currentStreak: number
  totalVolume: number
  averageIntensity: number
  nextWorkout: {
    name: string
    type: string
    duration: number
    exercises: number
  } | null
}

export function EnhancedTrainingDashboard({ userExperienceLevel, className }: TrainingDashboardProps) {
  const { user, profile } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')
  const [trainingStats, setTrainingStats] = useState<TrainingStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showRMCalculator, setShowRMCalculator] = useState(false)

  useEffect(() => {
    if (user) {
      loadTrainingStats()
    }
  }, [user])

  const loadTrainingStats = async () => {
    try {
      setIsLoading(true)
      console.log('📊 Cargando estadísticas de entrenamiento...')

      // Mock data for demonstration (in production, this would come from Supabase)
      const mockStats: TrainingStats = {
        weeklyWorkouts: 3,
        targetWorkouts: 4,
        currentStreak: 5,
        totalVolume: 12500,
        averageIntensity: 78,
        nextWorkout: {
          name: userExperienceLevel === 'beginner' ? 'Entrenamiento de Cuerpo Completo' : 'Push Day - Pecho y Hombros',
          type: userExperienceLevel === 'beginner' ? 'full_body' : 'push',
          duration: userExperienceLevel === 'beginner' ? 30 : 60,
          exercises: userExperienceLevel === 'beginner' ? 6 : 8
        }
      }

      setTrainingStats(mockStats)
      console.log('✅ Estadísticas de entrenamiento cargadas:', mockStats)

    } catch (error) {
      console.error('❌ Error cargando estadísticas de entrenamiento:', error)
      toast({
        title: "Error",
        description: "No se pudieron cargar las estadísticas de entrenamiento",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleStartWorkout = () => {
    if (userExperienceLevel === 'beginner') {
      router.push('/training/beginner/workout')
    } else {
      router.push('/training/execute-workout')
    }
  }

  const handleCreateRoutine = () => {
    if (userExperienceLevel === 'beginner') {
      router.push('/training/beginner/create-routine')
    } else {
      router.push('/training/routines/create')
    }
  }

  const handleViewProgress = () => {
    router.push('/training/progress')
  }

  const getExperienceBadgeColor = () => {
    switch (userExperienceLevel) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'advanced': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!trainingStats) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">No se pudieron cargar las estadísticas de entrenamiento</p>
          <Button onClick={loadTrainingStats} className="mt-4">
            Reintentar
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#1B237E] flex items-center">
            <Dumbbell className="h-6 w-6 mr-2 text-[#FEA800]" />
            Dashboard de Entrenamiento
          </h2>
          <p className="text-gray-600 mt-1">
            Gestiona tus entrenamientos y progreso
          </p>
        </div>
        <Badge className={getExperienceBadgeColor()}>
          <Sparkles className="h-4 w-4 mr-1" />
          {userExperienceLevel === 'beginner' ? 'Principiante' : 
           userExperienceLevel === 'intermediate' ? 'Intermedio' : 'Avanzado'}
        </Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Esta Semana</p>
                <p className="text-2xl font-bold text-[#1B237E]">{trainingStats.weeklyWorkouts}/{trainingStats.targetWorkouts}</p>
              </div>
              <Calendar className="h-8 w-8 text-[#FEA800]" />
            </div>
            <Progress value={(trainingStats.weeklyWorkouts / trainingStats.targetWorkouts) * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Racha Actual</p>
                <p className="text-2xl font-bold text-[#1B237E]">{trainingStats.currentStreak}</p>
              </div>
              <Zap className="h-8 w-8 text-[#FF6767]" />
            </div>
            <p className="text-xs text-gray-500 mt-1">días consecutivos</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Volumen Total</p>
                <p className="text-2xl font-bold text-[#1B237E]">{trainingStats.totalVolume.toLocaleString()}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-[#573353]" />
            </div>
            <p className="text-xs text-gray-500 mt-1">kg esta semana</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Intensidad</p>
                <p className="text-2xl font-bold text-[#1B237E]">{trainingStats.averageIntensity}%</p>
              </div>
              <Target className="h-8 w-8 text-[#B1AFE9]" />
            </div>
            <Progress value={trainingStats.averageIntensity} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Next Workout Card */}
      {trainingStats.nextWorkout && (
        <Card className="border-[#1B237E] border-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-[#1B237E]">Próximo Entrenamiento</span>
              <Badge variant="outline" className="text-[#FEA800] border-[#FEA800]">
                Recomendado
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-lg">{trainingStats.nextWorkout.name}</h3>
                <p className="text-gray-600">
                  {trainingStats.nextWorkout.duration} min • {trainingStats.nextWorkout.exercises} ejercicios
                </p>
              </div>
              <Button 
                onClick={handleStartWorkout}
                className="bg-[#FEA800] hover:bg-[#FEA800]/90 text-white"
              >
                <Play className="h-4 w-4 mr-2" />
                Comenzar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button 
          onClick={() => setShowRMCalculator(!showRMCalculator)}
          variant="outline" 
          className="h-16 flex flex-col items-center justify-center border-[#1B237E] text-[#1B237E] hover:bg-[#1B237E]/10"
        >
          <Calculator className="h-6 w-6 mb-1" />
          Calculadora RM
        </Button>

        <Button 
          onClick={handleCreateRoutine}
          variant="outline" 
          className="h-16 flex flex-col items-center justify-center border-[#573353] text-[#573353] hover:bg-[#573353]/10"
        >
          <Settings className="h-6 w-6 mb-1" />
          Crear Rutina
        </Button>

        <Button 
          onClick={handleViewProgress}
          variant="outline" 
          className="h-16 flex flex-col items-center justify-center border-[#FF6767] text-[#FF6767] hover:bg-[#FF6767]/10"
        >
          <TrendingUp className="h-6 w-6 mb-1" />
          Ver Progreso
        </Button>
      </div>

      {/* RM Calculator */}
      {showRMCalculator && (
        <RMCalculator 
          isExpanded={true}
          onSave={(data) => {
            console.log('RM guardado:', data)
            toast({
              title: "RM Guardado",
              description: "Tu registro de fuerza ha sido guardado correctamente"
            })
          }}
        />
      )}

      {/* Enhanced Personalization */}
      <EnhancedTrainingPersonalization />
    </div>
  )
}
