"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import {
  Watch,
  Heart,
  Activity,
  BarChart2,
  Zap,
  RefreshCw,
  Footprints,
  Flame,
  Clock,
  Moon,
  Smartphone,
  Bluetooth,
  Loader2,
  MapPin,
  Droplets,
  Thermometer,
  Waves,
  Compass
} from "lucide-react"
import { BluetoothService } from "@/lib/bluetooth-service"
import { supabase } from "@/lib/supabase-client"
import { useAuth } from "@/lib/contexts/auth-context"
import { LineChart, Line, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON>tesianGrid, <PERSON><PERSON><PERSON>, <PERSON>spons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Area } from 'recharts'
import { Map } from "@/components/ui/map"

// Tipos para los datos de actividad avanzados
interface AdvancedActivityData {
  steps: number
  caloriesBurned: number
  activeMinutes: number
  heartRate: {
    current: number
    resting: number
    max: number
    variability?: number
    zones?: {
      easy: number
      fatBurn: number
      cardio: number
      peak: number
    }
  }
  sleep: {
    duration: number
    quality: number
    stages?: {
      deep: number
      light: number
      rem: number
      awake: number
    }
    breathingRate?: number
    snoring?: number
  }
  gps?: {
    available: boolean
    coordinates: {
      latitude: number
      longitude: number
    }[]
    distance: number
    elevation: {
      gain: number
      loss: number
    }
  }
  temperature?: {
    skin: number
    ambient: number
  }
  hydration?: number
  stress?: number
  bloodOxygen?: number
  lastUpdated: Date
}

// Tipos para los dispositivos avanzados
interface AdvancedDevice {
  id: string
  name: string
  type: string
  connected: boolean
  batteryLevel?: number
  lastSynced?: Date
  firmwareVersion?: string
  sensors?: string[]
  capabilities?: string[]
}

export default function AdvancedWearableIntegration() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [activityData, setActivityData] = useState<AdvancedActivityData>({
    steps: 0,
    caloriesBurned: 0,
    activeMinutes: 0,
    heartRate: {
      current: 0,
      resting: 0,
      max: 0
    },
    sleep: {
      duration: 0,
      quality: 0
    },
    lastUpdated: new Date()
  })
  const [devices, setDevices] = useState<AdvancedDevice[]>([])
  const [historyData, setHistoryData] = useState<any[]>([])
  const [bluetoothService, setBluetoothService] = useState<BluetoothService | null>(null)
  const [showMap, setShowMap] = useState(false)

  // Inicializar el servicio Bluetooth
  useEffect(() => {
    const service = new BluetoothService()
    setBluetoothService(service)

    if (user?.id) {
      service.setUserId(user.id)
    }

    return () => {
      // Limpiar recursos si es necesario
    }
  }, [user?.id])

  // Cargar datos de actividad y dispositivos
  useEffect(() => {
    if (!user?.id) return

    const loadData = async () => {
      setIsLoading(true)
      try {
        // Cargar dispositivos
        await loadDevices()

        // Cargar datos de actividad
        await loadActivityData()

        // Cargar datos históricos
        await loadHistoryData()
      } catch (error) {
        console.error("Error al cargar datos:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [user?.id])

  // Cargar dispositivos conectados
  const loadDevices = async () => {
    try {
      // Intentar cargar desde Supabase
      const { data, error } = await supabase
        .from('connected_wearables')
        .select('*')
        .eq('user_id', user?.id)

      if (error) {
        console.warn("Error al cargar dispositivos:", error)
        // Usar datos de ejemplo si hay error
        setDevices([
          {
            id: "device-1",
            name: "Garmin Forerunner 945",
            type: "smartwatch",
            connected: true,
            batteryLevel: 78,
            lastSynced: new Date(Date.now() - 3600000), // 1 hora atrás
            firmwareVersion: "10.10",
            sensors: ["heart_rate", "gps", "accelerometer", "gyroscope", "barometer", "thermometer", "pulse_ox"],
            capabilities: ["activity_tracking", "sleep_tracking", "stress_tracking", "workout_detection", "gps_tracking", "heart_rate_variability"]
          },
          {
            id: "device-2",
            name: "Polar H10",
            type: "heart_rate",
            connected: false,
            batteryLevel: 92,
            lastSynced: new Date(Date.now() - 86400000), // 1 día atrás
            firmwareVersion: "3.0.5",
            sensors: ["heart_rate", "ecg"],
            capabilities: ["heart_rate_tracking", "heart_rate_variability", "ecg_recording"]
          }
        ])
        return
      }

      if (data && data.length > 0) {
        const formattedDevices = data.map(device => ({
          id: device.device_id,
          name: device.device_name,
          type: device.device_type,
          connected: device.status === 'active',
          batteryLevel: device.battery_level || Math.floor(Math.random() * 30) + 70, // Simular nivel de batería
          lastSynced: device.last_sync ? new Date(device.last_sync) : new Date(),
          firmwareVersion: "10.10",
          sensors: ["heart_rate", "gps", "accelerometer", "gyroscope", "barometer", "thermometer", "pulse_ox"],
          capabilities: ["activity_tracking", "sleep_tracking", "stress_tracking", "workout_detection", "gps_tracking", "heart_rate_variability"]
        }))
        setDevices(formattedDevices)
      } else {
        // Usar datos de ejemplo si no hay dispositivos
        setDevices([
          {
            id: "device-1",
            name: "Garmin Forerunner 945",
            type: "smartwatch",
            connected: true,
            batteryLevel: 78,
            lastSynced: new Date(Date.now() - 3600000), // 1 hora atrás
            firmwareVersion: "10.10",
            sensors: ["heart_rate", "gps", "accelerometer", "gyroscope", "barometer", "thermometer", "pulse_ox"],
            capabilities: ["activity_tracking", "sleep_tracking", "stress_tracking", "workout_detection", "gps_tracking", "heart_rate_variability"]
          }
        ])
      }
    } catch (error) {
      console.error("Error al cargar dispositivos:", error)
      // Usar datos de ejemplo en caso de error
      setDevices([
        {
          id: "device-1",
          name: "Garmin Forerunner 945",
          type: "smartwatch",
          connected: true,
          batteryLevel: 78,
          lastSynced: new Date(Date.now() - 3600000), // 1 hora atrás
          firmwareVersion: "10.10",
          sensors: ["heart_rate", "gps", "accelerometer", "gyroscope", "barometer", "thermometer", "pulse_ox"],
          capabilities: ["activity_tracking", "sleep_tracking", "stress_tracking", "workout_detection", "gps_tracking", "heart_rate_variability"]
        }
      ])
    }
  }

  // Cargar datos de actividad
  const loadActivityData = async () => {
    try {
      // Intentar cargar desde Supabase
      const { data, error } = await supabase
        .from('wearable_data')
        .select('*')
        .eq('user_id', user?.id)
        .order('date', { ascending: false })
        .limit(1)

      if (error) {
        console.warn("Error al cargar datos de actividad:", error)
        // Usar datos de ejemplo si hay error
        generateSampleActivityData()
        return
      }

      if (data && data.length > 0) {
        const latestData = data[0]
        setActivityData({
          steps: latestData.steps || 0,
          caloriesBurned: latestData.calories_burned || 0,
          activeMinutes: latestData.active_minutes || 0,
          heartRate: {
            current: latestData.heart_rate?.average || 0,
            resting: latestData.heart_rate?.resting || 0,
            max: latestData.heart_rate?.max || 0,
            variability: latestData.heart_rate?.variability || 0,
            zones: latestData.heart_rate?.zones || {
              easy: 25,
              fatBurn: 40,
              cardio: 25,
              peak: 10
            }
          },
          sleep: {
            duration: latestData.sleep?.duration || 0,
            quality: latestData.sleep?.score || 0,
            stages: latestData.sleep?.stages || {
              deep: 1.5,
              light: 4,
              rem: 1.5,
              awake: 0.5
            },
            breathingRate: latestData.sleep?.breathing_rate || 14,
            snoring: latestData.sleep?.snoring || 10
          },
          gps: latestData.gps || {
            available: true,
            coordinates: [
              { latitude: 40.7128, longitude: -74.0060 },
              { latitude: 40.7129, longitude: -74.0061 },
              { latitude: 40.7130, longitude: -74.0062 }
            ],
            distance: 5.2,
            elevation: {
              gain: 120,
              loss: 80
            }
          },
          temperature: latestData.temperature || {
            skin: 32.5,
            ambient: 22.0
          },
          hydration: latestData.hydration || 65,
          stress: latestData.stress_level || 42,
          bloodOxygen: latestData.blood_oxygen || 98,
          lastUpdated: new Date(latestData.created_at)
        })
      } else {
        // Usar datos de ejemplo si no hay datos
        generateSampleActivityData()
      }
    } catch (error) {
      console.error("Error al cargar datos de actividad:", error)
      // Usar datos de ejemplo en caso de error
      generateSampleActivityData()
    }
  }

  // Generar datos de actividad de ejemplo
  const generateSampleActivityData = () => {
    const now = new Date()
    const steps = Math.floor(Math.random() * 5000) + 3000
    const caloriesBurned = Math.floor(steps * 0.05)
    const activeMinutes = Math.floor(steps / 100)

    setActivityData({
      steps,
      caloriesBurned,
      activeMinutes,
      heartRate: {
        current: Math.floor(Math.random() * 20) + 65,
        resting: 62,
        max: 142,
        variability: 45,
        zones: {
          easy: 25,
          fatBurn: 40,
          cardio: 25,
          peak: 10
        }
      },
      sleep: {
        duration: 7.2,
        quality: 82,
        stages: {
          deep: 1.5,
          light: 4,
          rem: 1.5,
          awake: 0.5
        },
        breathingRate: 14,
        snoring: 10
      },
      gps: {
        available: true,
        coordinates: [
          { latitude: 40.7128, longitude: -74.0060 },
          { latitude: 40.7129, longitude: -74.0061 },
          { latitude: 40.7130, longitude: -74.0062 }
        ],
        distance: 5.2,
        elevation: {
          gain: 120,
          loss: 80
        }
      },
      temperature: {
        skin: 32.5,
        ambient: 22.0
      },
      hydration: 65,
      stress: 42,
      bloodOxygen: 98,
      lastUpdated: now
    })
  }

  // Cargar datos históricos
  const loadHistoryData = async () => {
    try {
      // Intentar cargar desde Supabase
      const { data, error } = await supabase
        .from('wearable_data')
        .select('date, steps, calories_burned, active_minutes, heart_rate, stress_level, blood_oxygen')
        .eq('user_id', user?.id)
        .order('date', { ascending: false })
        .limit(7)

      if (error) {
        console.warn("Error al cargar datos históricos:", error)
        // Usar datos de ejemplo si hay error
        generateSampleHistoryData()
        return
      }

      if (data && data.length > 0) {
        const formattedData = data.map(item => ({
          date: new Date(item.date).toLocaleDateString('es-ES', { weekday: 'short' }),
          steps: item.steps || 0,
          calories: item.calories_burned || 0,
          activeMinutes: item.active_minutes || 0,
          heartRate: item.heart_rate?.average || 0,
          stress: item.stress_level || 0,
          bloodOxygen: item.blood_oxygen || 0
        })).reverse()

        setHistoryData(formattedData)
      } else {
        // Usar datos de ejemplo si no hay datos
        generateSampleHistoryData()
      }
    } catch (error) {
      console.error("Error al cargar datos históricos:", error)
      // Usar datos de ejemplo en caso de error
      generateSampleHistoryData()
    }
  }

  // Generar datos históricos de ejemplo
  const generateSampleHistoryData = () => {
    const days = ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom']
    const data = days.map((day, index) => ({
      date: day,
      steps: Math.floor(Math.random() * 5000) + 3000,
      calories: Math.floor(Math.random() * 300) + 200,
      activeMinutes: Math.floor(Math.random() * 60) + 30,
      heartRate: Math.floor(Math.random() * 20) + 65,
      stress: Math.floor(Math.random() * 50) + 20,
      bloodOxygen: Math.floor(Math.random() * 3) + 96
    }))

    setHistoryData(data)
  }

  // Refrescar datos
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await loadActivityData()
      await loadHistoryData()

      toast({
        title: "Datos actualizados",
        description: "Los datos de actividad han sido actualizados correctamente"
      })
    } catch (error) {
      console.error("Error al refrescar datos:", error)
      toast({
        title: "Error",
        description: "No se pudieron actualizar los datos",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Conectar nuevo dispositivo
  const handleConnectDevice = async () => {
    if (!bluetoothService) {
      toast({
        title: "Error",
        description: "El servicio Bluetooth no está disponible",
        variant: "destructive"
      })
      return
    }

    setIsConnecting(true)
    try {
      const newDevices = await bluetoothService.scanForHeartRateDevices()

      if (newDevices && newDevices.length > 0) {
        toast({
          title: "Dispositivo encontrado",
          description: `Se ha conectado a ${newDevices[0].name}`
        })

        // Actualizar lista de dispositivos
        await loadDevices()
      } else {
        toast({
          title: "No se encontraron dispositivos",
          description: "No se encontraron dispositivos compatibles cercanos"
        })
      }
    } catch (error) {
      console.error("Error al conectar dispositivo:", error)
      toast({
        title: "Error",
        description: "No se pudo conectar al dispositivo",
        variant: "destructive"
      })
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Integración Avanzada con Wearables</h2>
          <p className="text-muted-foreground">Monitorea tu actividad física con datos avanzados de tus dispositivos</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span className="ml-2 hidden md:inline">Actualizar</span>
          </Button>
          <Button
            size="sm"
            onClick={handleConnectDevice}
            disabled={isConnecting}
          >
            {isConnecting ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Bluetooth className="h-4 w-4 mr-2" />
            )}
            Conectar Dispositivo
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <span>Resumen</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <BarChart2 className="h-4 w-4" />
            <span>Datos Avanzados</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>Historial</span>
          </TabsTrigger>
          <TabsTrigger value="devices" className="flex items-center gap-2">
            <Watch className="h-4 w-4" />
            <span>Dispositivos</span>
          </TabsTrigger>
        </TabsList>

        {/* Resto del contenido se implementará con el editor str-replace */}
      </Tabs>
    </div>
  )
}
