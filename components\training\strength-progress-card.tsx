"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, Card<PERSON>DContent, Card3<PERSON><PERSON>eader, Card3DTitle } from "@/components/ui/card-3d"
import { Button3D } from "@/components/ui/button-3d"
import { Progress3D } from "@/components/ui/progress-3d"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import {
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ChevronRight,
  RefreshCw,
  Zap,
  Battery,
  BatteryCharging,
  BatteryWarning
} from "lucide-react"
import { useAuth } from "@/lib/contexts/auth-context"
import { supabase } from "@/lib/supabase-client"
import { processSupabaseResponse, processSingleRecord } from "@/lib/supabase-utils"

interface StrengthData {
  date: string;
  bench: number;
  squat: number;
  deadlift: number;
  overhead: number;
}

interface FatigueData {
  currentFatigue: number;
  recoveryCapacity: number;
  readinessToTrain: number;
  muscleGroupFatigue: Record<string, number>;
}

export function StrengthProgressCard() {
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [strengthData, setStrengthData] = useState<StrengthData[]>([])
  const [fatigueData, setFatigueData] = useState<FatigueData | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Cargar datos de progreso de fuerza y fatiga
  useEffect(() => {
    const loadData = async () => {
      if (!user) return

      setIsLoading(true)

      try {
        try {
          // Cargar datos de progreso de fuerza desde progression_history
          const { data: strengthLogs, error: strengthError } = await supabase
            .from('progression_history')
            .select('*')
            .eq('user_id', user.id)
            .in('exercise_id', ['bench_press', 'squat', 'deadlift', 'overhead_press'])
            .order('created_at', { ascending: true })
            .limit(40) // Más registros para obtener datos de múltiples ejercicios

          // Usar nuestra nueva función de utilidad para procesar la respuesta
          const { data: processedData, error: processedError, usingFallback } = processSupabaseResponse(
            strengthLogs,
            strengthError,
            getSampleStrengthData(),
            "Carga de datos de progreso de fuerza"
          )

          // Mostrar toast si hay error (pero no para errores vacíos que ya se manejan automáticamente)
          if (processedError && processedError.code !== 'EMPTY_RESPONSE') {
            toast({
              title: "Error de carga",
              description: "No se pudieron cargar los datos de progreso. Usando datos de ejemplo.",
              variant: "destructive"
            })
          } else if (processedError && processedError.code === 'EMPTY_RESPONSE') {
            // Para errores vacíos, solo mostrar un mensaje informativo
            console.info('🔄 Usando datos de ejemplo debido a respuesta vacía de Supabase');
          }

          // Si estamos usando datos de respaldo, simplemente establecerlos
          if (usingFallback) {
            setStrengthData(processedData as StrengthData[])
          } else {
            // Si tenemos datos reales, transformarlos al formato esperado
            // Agrupar por fecha y ejercicio
            const groupedData: Record<string, any> = {}

            if (Array.isArray(processedData)) {
              processedData.forEach((log: any) => {
                const date = log.created_at ? new Date(log.created_at).toLocaleDateString() : new Date().toLocaleDateString()

                if (!groupedData[date]) {
                  groupedData[date] = {
                    date,
                    bench: 0,
                    squat: 0,
                    deadlift: 0,
                    overhead: 0
                  }
                }

                // Mapear exercise_id a los campos correspondientes
                if (log.exercise_id === 'bench_press') {
                  groupedData[date].bench = log.new_value || 0
                } else if (log.exercise_id === 'squat') {
                  groupedData[date].squat = log.new_value || 0
                } else if (log.exercise_id === 'deadlift') {
                  groupedData[date].deadlift = log.new_value || 0
                } else if (log.exercise_id === 'overhead_press') {
                  groupedData[date].overhead = log.new_value || 0
                }
              })
            }

            const formattedData = Object.values(groupedData).sort((a: any, b: any) =>
              new Date(a.date).getTime() - new Date(b.date).getTime()
            )

            setStrengthData(formattedData as StrengthData[])
          }
        } catch (strengthLoadError) {
          console.error("Error inesperado al cargar datos de fuerza:", strengthLoadError)
          setStrengthData(getSampleStrengthData())
        }

        try {
          // Cargar datos de fatiga desde fatigue_metrics
          const { data: fatigueLog, error: fatigueError } = await supabase
            .from('fatigue_metrics')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single()

          // Usar nuestra nueva función de utilidad para procesar la respuesta de un solo registro
          const { data: processedData, error: processedError, usingFallback } = processSingleRecord(
            fatigueLog,
            fatigueError,
            getSampleFatigueData(),
            "Carga de datos de fatiga"
          )

          // Mostrar toast si hay error (excepto para PGRST116 y EMPTY_RESPONSE que se manejan automáticamente)
          if (processedError && processedError.code !== 'PGRST116' && processedError.code !== 'EMPTY_RESPONSE') {
            toast({
              title: "Error de carga",
              description: "No se pudieron cargar los datos de fatiga. Usando datos de ejemplo.",
              variant: "destructive"
            })
          } else if (processedError && processedError.code === 'EMPTY_RESPONSE') {
            // Para errores vacíos, solo mostrar un mensaje informativo
            console.info('🔄 Usando datos de ejemplo de fatiga debido a respuesta vacía de Supabase');
          }

          // Si estamos usando datos de respaldo, simplemente establecerlos
          if (usingFallback) {
            setFatigueData(processedData as FatigueData)
          } else {
            // Si tenemos datos reales, procesarlos usando los campos de fatigue_metrics
            const defaultFatigueData = getSampleFatigueData();

            // Crear objeto de fatiga por grupo muscular basado en los datos disponibles
            let muscleGroupFatigue = defaultFatigueData.muscleGroupFatigue;
            if (processedData && processedData.muscles_soreness) {
              // Usar muscles_soreness como base para fatiga muscular
              muscleGroupFatigue = {
                "pecho": processedData.muscles_soreness || 50,
                "espalda": processedData.muscles_soreness || 40,
                "piernas": processedData.muscles_soreness || 60,
                "hombros": processedData.muscles_soreness || 30,
                "brazos": processedData.muscles_soreness || 45,
                "core": processedData.muscles_soreness || 35
              };
            }

            setFatigueData({
              currentFatigue: processedData && typeof processedData.overall_fatigue_score === 'number'
                ? processedData.overall_fatigue_score
                : (processedData && typeof processedData.perceived_fatigue === 'number'
                  ? processedData.perceived_fatigue * 10 // Convertir escala 1-10 a porcentaje
                  : defaultFatigueData.currentFatigue),
              recoveryCapacity: processedData && typeof processedData.energy_level === 'number'
                ? processedData.energy_level * 10 // Convertir escala 1-10 a porcentaje
                : defaultFatigueData.recoveryCapacity,
              readinessToTrain: processedData && typeof processedData.motivation === 'number'
                ? processedData.motivation * 10 // Convertir escala 1-10 a porcentaje
                : defaultFatigueData.readinessToTrain,
              muscleGroupFatigue: muscleGroupFatigue
            })
          }
        } catch (fatigueLoadError) {
          console.error("Error inesperado al cargar datos de fatiga:", fatigueLoadError)
          setFatigueData(getSampleFatigueData())
        }
      } catch (error) {
        console.error("Error inesperado al cargar datos:", error)
        toast({
          title: "Error inesperado",
          description: "Ocurrió un error al procesar los datos. Usando datos de ejemplo.",
          variant: "destructive"
        })
        // Usar datos de ejemplo en caso de error
        setStrengthData(getSampleStrengthData())
        setFatigueData(getSampleFatigueData())
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [user, toast])

  // Función para refrescar los datos
  const handleRefresh = async () => {
    if (!user || isRefreshing) return

    setIsRefreshing(true)

    try {
      // Generar datos simulados como respaldo
      const generateSimulatedData = () => {
        return {
          ...getSampleFatigueData(),
          currentFatigue: Math.floor(Math.random() * 40) + 30, // 30-70
          recoveryCapacity: Math.floor(Math.random() * 30) + 60, // 60-90
          readinessToTrain: Math.floor(Math.random() * 30) + 50 // 50-80
        };
      };

      // Intentar cargar datos reales primero
      try {
        const { data: fatigueLog, error: fatigueError } = await supabase
          .from('fatigue_logs')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single()

        // Usar nuestra nueva función de utilidad para procesar la respuesta
        const { data: processedData, error: processedError, usingFallback } = processSingleRecord(
          fatigueLog,
          fatigueError,
          generateSimulatedData(),
          "Actualización de datos de fatiga"
        )

        // Si estamos usando datos de respaldo, mostrar mensaje apropiado
        if (usingFallback) {
          // Simular una pequeña demora para que parezca que estamos procesando
          if (fatigueError) {
            await new Promise(resolve => setTimeout(resolve, 1000))
          }

          setFatigueData(processedData as FatigueData)

          toast({
            title: "Datos actualizados",
            description: "Se han generado nuevos datos de fatiga simulados"
          })
        } else {
          // Si tenemos datos reales, procesarlos
          const defaultFatigueData = getSampleFatigueData();

          // Verificar si muscle_group_fatigue es un objeto válido
          let muscleGroupFatigue = defaultFatigueData.muscleGroupFatigue;
          if (processedData &&
              processedData.muscle_group_fatigue &&
              typeof processedData.muscle_group_fatigue === 'object' &&
              Object.keys(processedData.muscle_group_fatigue).length > 0) {
            muscleGroupFatigue = processedData.muscle_group_fatigue;
          }

          const updatedFatigue = {
            currentFatigue: processedData && typeof processedData.current_fatigue === 'number'
              ? processedData.current_fatigue
              : defaultFatigueData.currentFatigue,
            recoveryCapacity: processedData && typeof processedData.recovery_capacity === 'number'
              ? processedData.recovery_capacity
              : defaultFatigueData.recoveryCapacity,
            readinessToTrain: processedData && typeof processedData.readiness_to_train === 'number'
              ? processedData.readiness_to_train
              : defaultFatigueData.readinessToTrain,
            muscleGroupFatigue: muscleGroupFatigue
          };

          setFatigueData(updatedFatigue);

          toast({
            title: "Datos actualizados",
            description: "Los datos de fatiga han sido actualizados con información real"
          });
        }
      } catch (innerError) {
        console.error("Error al intentar cargar datos reales:", innerError)

        // Usar datos simulados como respaldo
        const updatedFatigue = generateSimulatedData();
        setFatigueData(updatedFatigue);

        toast({
          title: "Datos actualizados",
          description: "Se han generado nuevos datos de fatiga simulados"
        });
      }
    } catch (error) {
      console.error("Error inesperado al refrescar datos:", error)

      // Usar datos simulados como último recurso
      const updatedFatigue = {
        ...getSampleFatigueData(),
        currentFatigue: Math.floor(Math.random() * 40) + 30,
        recoveryCapacity: Math.floor(Math.random() * 30) + 60,
        readinessToTrain: Math.floor(Math.random() * 30) + 50
      };

      setFatigueData(updatedFatigue);

      toast({
        title: "Error",
        description: "Ocurrió un error al actualizar los datos. Se han generado datos simulados.",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Navegar a la página de detalles
  const handleViewDetails = () => {
    router.push('/training/progress')
  }

  // Obtener color según nivel de fatiga
  const getFatigueColor = (value: number) => {
    if (value < 30) return "bg-green-500"
    if (value < 60) return "bg-yellow-500"
    if (value < 80) return "bg-orange-500"
    return "bg-red-500"
  }

  // Obtener icono según nivel de fatiga
  const getFatigueIcon = (value: number) => {
    if (value < 30) return <BatteryCharging className="h-5 w-5 text-green-500" />
    if (value < 60) return <Battery className="h-5 w-5 text-yellow-500" />
    if (value < 80) return <BatteryWarning className="h-5 w-5 text-orange-500" />
    return <BatteryWarning className="h-5 w-5 text-red-500" />
  }

  return (
    <Card3D>
      <Card3DHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
            <Card3DTitle>Progreso de Fuerza y Gestión de la Fatiga</Card3DTitle>
          </div>
          <Button3D
            variant="ghost"
            size="icon"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button3D>
        </div>
      </Card3DHeader>

      <Card3DContent>
        <Tabs defaultValue="fatigue" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="fatigue" className="flex items-center gap-1">
              <Zap className="h-4 w-4" />
              <span>Fatiga</span>
            </TabsTrigger>
            <TabsTrigger value="strength" className="flex items-center gap-1">
              <Dumbbell className="h-4 w-4" />
              <span>Fuerza</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="fatigue" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="h-8 w-8 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
              </div>
            ) : fatigueData && typeof fatigueData === 'object' ? (
              <>
                <div className="space-y-3">
                  {/* Fatiga actual */}
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Fatiga actual</span>
                      <span className="text-sm font-medium">
                        {typeof fatigueData.currentFatigue === 'number'
                          ? Math.round(fatigueData.currentFatigue)
                          : 0}%
                      </span>
                    </div>
                    <Progress3D
                      value={typeof fatigueData.currentFatigue === 'number' ? fatigueData.currentFatigue : 0}
                      max={100}
                      className={`h-2 ${getFatigueColor(fatigueData.currentFatigue || 0)}`}
                    />
                  </div>

                  {/* Capacidad de recuperación */}
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Capacidad de recuperación</span>
                      <span className="text-sm font-medium">
                        {typeof fatigueData.recoveryCapacity === 'number'
                          ? Math.round(fatigueData.recoveryCapacity)
                          : 0}%
                      </span>
                    </div>
                    <Progress3D
                      value={typeof fatigueData.recoveryCapacity === 'number' ? fatigueData.recoveryCapacity : 0}
                      max={100}
                      className="h-2 bg-blue-500"
                    />
                  </div>

                  {/* Disposición para entrenar */}
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Disposición para entrenar</span>
                      <span className="text-sm font-medium">
                        {typeof fatigueData.readinessToTrain === 'number'
                          ? Math.round(fatigueData.readinessToTrain)
                          : 0}%
                      </span>
                    </div>
                    <Progress3D
                      value={typeof fatigueData.readinessToTrain === 'number' ? fatigueData.readinessToTrain : 0}
                      max={100}
                      className="h-2 bg-green-500"
                    />
                  </div>
                </div>

                {/* Fatiga por grupo muscular */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Fatiga por grupo muscular</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {fatigueData.muscleGroupFatigue && typeof fatigueData.muscleGroupFatigue === 'object'
                      ? Object.entries(fatigueData.muscleGroupFatigue).map(([muscle, value]) => (
                        <div key={muscle} className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="capitalize">{muscle}</span>
                            <span>{typeof value === 'number' ? Math.round(value) : 0}%</span>
                          </div>
                          <Progress3D
                            value={typeof value === 'number' ? value : 0}
                            max={100}
                            className={`h-1 ${getFatigueColor(typeof value === 'number' ? value : 0)}`}
                          />
                        </div>
                      ))
                      : (
                        <div className="col-span-2 text-center py-2">
                          <p className="text-xs text-gray-500">No hay datos de fatiga muscular disponibles</p>
                        </div>
                      )
                    }
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">No hay datos de fatiga disponibles</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="strength">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="h-8 w-8 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
              </div>
            ) : strengthData && Array.isArray(strengthData) && strengthData.length > 1 ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  {/* Press de banca */}
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">Press de banca</div>
                    <div className="text-lg font-semibold">
                      {strengthData[strengthData.length - 1]?.bench || 0} kg
                    </div>
                    <div className="text-xs text-green-500 mt-1">
                      {(() => {
                        const latest = strengthData[strengthData.length - 1]?.bench || 0;
                        const first = strengthData[0]?.bench || 0;
                        const diff = latest - first;
                        return `+${diff.toFixed(1)} kg`;
                      })()}
                    </div>
                  </div>

                  {/* Sentadilla */}
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">Sentadilla</div>
                    <div className="text-lg font-semibold">
                      {strengthData[strengthData.length - 1]?.squat || 0} kg
                    </div>
                    <div className="text-xs text-green-500 mt-1">
                      {(() => {
                        const latest = strengthData[strengthData.length - 1]?.squat || 0;
                        const first = strengthData[0]?.squat || 0;
                        const diff = latest - first;
                        return `+${diff.toFixed(1)} kg`;
                      })()}
                    </div>
                  </div>

                  {/* Peso muerto */}
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">Peso muerto</div>
                    <div className="text-lg font-semibold">
                      {strengthData[strengthData.length - 1]?.deadlift || 0} kg
                    </div>
                    <div className="text-xs text-green-500 mt-1">
                      {(() => {
                        const latest = strengthData[strengthData.length - 1]?.deadlift || 0;
                        const first = strengthData[0]?.deadlift || 0;
                        const diff = latest - first;
                        return `+${diff.toFixed(1)} kg`;
                      })()}
                    </div>
                  </div>

                  {/* Press militar */}
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">Press militar</div>
                    <div className="text-lg font-semibold">
                      {strengthData[strengthData.length - 1]?.overhead || 0} kg
                    </div>
                    <div className="text-xs text-green-500 mt-1">
                      {(() => {
                        const latest = strengthData[strengthData.length - 1]?.overhead || 0;
                        const first = strengthData[0]?.overhead || 0;
                        const diff = latest - first;
                        return `+${diff.toFixed(1)} kg`;
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">No hay datos de progreso disponibles</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <Button3D
          variant="outline"
          className="w-full mt-4 flex items-center justify-center"
          onClick={handleViewDetails}
        >
          Ver detalles
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button3D>
      </Card3DContent>
    </Card3D>
  )
}

// Datos de ejemplo para usar cuando hay errores o no hay datos
function getSampleStrengthData(): StrengthData[] {
  const today = new Date()
  const data: StrengthData[] = []

  for (let i = 0; i < 5; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - (i * 14)) // Cada 2 semanas

    data.unshift({
      date: date.toLocaleDateString(),
      bench: 70 + i * 2.5,
      squat: 100 + i * 5,
      deadlift: 120 + i * 5,
      overhead: 45 + i * 2.5
    })
  }

  return data
}

function getSampleFatigueData(): FatigueData {
  return {
    currentFatigue: 45,
    recoveryCapacity: 75,
    readinessToTrain: 65,
    muscleGroupFatigue: {
      "pecho": 60,
      "espalda": 40,
      "piernas": 75,
      "hombros": 30,
      "brazos": 50,
      "core": 25
    }
  }
}
