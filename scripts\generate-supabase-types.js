/**
 * Supabase Types Generator
 * 
 * This script generates TypeScript types from the Supabase database schema.
 * It creates a types file that can be used in the application.
 * 
 * Usage:
 * node scripts/generate-supabase-types.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Supabase Management API configuration
const SUPABASE_PROJECT_ID = process.env.SUPABASE_PROJECT_ID || 'soviwrzrgskhvgcmujfj';
const SUPABASE_API_KEY = process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_API_KEY) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is required');
  process.exit(1);
}

// Supabase Management API client
const supabaseManagementApi = axios.create({
  baseURL: 'https://api.supabase.com/v1',
  headers: {
    'Authorization': `Bearer ${SUPABASE_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// Output file path
const outputFilePath = path.join(__dirname, '..', 'lib', 'types', 'supabase-generated.ts');

/**
 * Get database schema information
 * @returns {Promise<any>} - Database schema information
 */
async function getDatabaseSchema() {
  try {
    const response = await supabaseManagementApi.get(`/projects/${SUPABASE_PROJECT_ID}/database/schema`);
    return response.data;
  } catch (error) {
    console.error('Error getting database schema:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Convert PostgreSQL type to TypeScript type
 * @param {string} pgType - PostgreSQL type
 * @returns {string} - TypeScript type
 */
function pgTypeToTsType(pgType) {
  const typeMap = {
    'uuid': 'string',
    'text': 'string',
    'varchar': 'string',
    'char': 'string',
    'character varying': 'string',
    'character': 'string',
    'integer': 'number',
    'int': 'number',
    'int4': 'number',
    'int8': 'number',
    'bigint': 'number',
    'smallint': 'number',
    'decimal': 'number',
    'numeric': 'number',
    'real': 'number',
    'double precision': 'number',
    'float': 'number',
    'float4': 'number',
    'float8': 'number',
    'boolean': 'boolean',
    'bool': 'boolean',
    'timestamp': 'string',
    'timestamptz': 'string',
    'timestamp with time zone': 'string',
    'timestamp without time zone': 'string',
    'date': 'string',
    'time': 'string',
    'timetz': 'string',
    'time with time zone': 'string',
    'time without time zone': 'string',
    'interval': 'string',
    'json': 'any',
    'jsonb': 'any',
    'array': 'any[]',
    'text[]': 'string[]',
    'varchar[]': 'string[]',
    'integer[]': 'number[]',
    'int[]': 'number[]',
    'int4[]': 'number[]',
    'int8[]': 'number[]',
    'bigint[]': 'number[]',
    'smallint[]': 'number[]',
    'decimal[]': 'number[]',
    'numeric[]': 'number[]',
    'real[]': 'number[]',
    'double precision[]': 'number[]',
    'float[]': 'number[]',
    'float4[]': 'number[]',
    'float8[]': 'number[]',
    'boolean[]': 'boolean[]',
    'bool[]': 'boolean[]',
    'uuid[]': 'string[]'
  };

  // Handle array types
  if (pgType.endsWith('[]')) {
    const baseType = pgType.slice(0, -2);
    return `${pgTypeToTsType(baseType)}[]`;
  }

  return typeMap[pgType] || 'any';
}

/**
 * Generate TypeScript interface for a table
 * @param {Object} table - Table schema
 * @returns {string} - TypeScript interface
 */
function generateTableInterface(table) {
  const interfaceName = table.name.replace(/([-_][a-z])/g, group => group.toUpperCase().replace('-', '').replace('_', ''));
  const pascalCaseName = interfaceName.charAt(0).toUpperCase() + interfaceName.slice(1);
  
  let interfaceString = `export interface ${pascalCaseName} {\n`;
  
  table.columns.forEach(column => {
    const columnName = column.name;
    const isNullable = column.is_nullable;
    const tsType = pgTypeToTsType(column.type);
    
    interfaceString += `  ${columnName}${isNullable ? '?' : ''}: ${tsType};\n`;
  });
  
  interfaceString += '}\n\n';
  
  return interfaceString;
}

/**
 * Generate TypeScript types file
 * @param {Object} schema - Database schema
 * @returns {string} - TypeScript types file content
 */
function generateTypesFile(schema) {
  let content = `/**
 * Supabase Generated Types
 * 
 * This file is auto-generated from the Supabase database schema.
 * Do not edit this file directly.
 * 
 * Generated on: ${new Date().toISOString()}
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

`;

  // Generate interfaces for tables
  schema.tables.forEach(table => {
    content += generateTableInterface(table);
  });

  // Generate Database type
  content += `export interface Database {\n`;
  content += `  public: {\n`;
  content += `    Tables: {\n`;
  
  schema.tables.forEach(table => {
    const interfaceName = table.name.replace(/([-_][a-z])/g, group => group.toUpperCase().replace('-', '').replace('_', ''));
    const pascalCaseName = interfaceName.charAt(0).toUpperCase() + interfaceName.slice(1);
    
    content += `      ${table.name}: ${pascalCaseName};\n`;
  });
  
  content += `    };\n`;
  content += `  };\n`;
  content += `}\n`;

  return content;
}

/**
 * Main function
 */
async function main() {
  console.log('Generating Supabase types...');
  
  try {
    // Get database schema
    const schema = await getDatabaseSchema();
    
    // Generate types file
    const typesFileContent = generateTypesFile(schema);
    
    // Create directory if it doesn't exist
    const dir = path.dirname(outputFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Write types file
    fs.writeFileSync(outputFilePath, typesFileContent);
    
    console.log(`Types file generated successfully: ${outputFilePath}`);
  } catch (error) {
    console.error('Error generating types:', error);
    process.exit(1);
  }
}

// Run the main function
main();
