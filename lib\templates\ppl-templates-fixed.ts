"use strict";

/**
 * Push/Pull/Legs (PPL) Templates
 *
 * Implements advanced PPL training templates with:
 * - 5-day and 7-day frequency options
 * - Based on <PERSON> and <PERSON> training systems
 * - Periodized structure with volume and intensity phases
 * - Programmed deload weeks
 */

import { v4 as uuidv4 } from "uuid";
import { WorkoutRoutine, WorkoutDay, ExerciseSet } from "@/lib/types/training";
import { TrainingLevel, TrainingGoal } from "@/lib/types/periodization";

// PPL Template Types
export type PplFrequency = 5 | 6 | 7;
export type PplVariant = 'standard' | 'nippard' | 'cbum' | 'volume_focus' | 'strength_focus';
export type PplPhase = 'volume' | 'intensity' | 'strength' | 'deload';

// Interface for PPL template configuration
export interface PplTemplateConfig {
  userId: string;
  level: TrainingLevel;
  goal: TrainingGoal;
  frequency: PplFrequency;
  variant: PplVariant;
  phase?: PplPhase;
  includeDeload?: boolean;
  priorityMuscleGroups?: string[];
  equipment?: string[];
}

/**
 * Create a Push/Pull/Legs (PPL) routine based on configuration
 */
export function createPplRoutine(config: PplTemplateConfig): WorkoutRoutine {
  const {
    userId,
    level,
    goal,
    frequency,
    variant,
    phase = 'volume',
    includeDeload = true,
    priorityMuscleGroups = [],
    equipment = []
  } = config;

  // Generate routine name
  const routineName = generateRoutineName(frequency, variant, phase);

  // Generate routine description
  const routineDescription = generateRoutineDescription(frequency, variant, level, goal, phase);

  // Generate workout days based on frequency
  const workoutDays = generateWorkoutDays(frequency, variant, level, phase, priorityMuscleGroups, equipment);

  // Create the routine
  const routine: WorkoutRoutine = {
    id: uuidv4(),
    userId,
    name: routineName,
    description: routineDescription,
    days: workoutDays,
    frequency: frequency,
    goal: goal,
    level: level,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    includesDeload: includeDeload,
    deloadFrequency: level === 'beginner' ? 8 : level === 'intermediate' ? 6 : 4,
    deloadStrategy: goal === 'strength' ? 'intensity' : 'volume',
    source: variant === 'nippard' ? 'Jeff Nippard' : variant === 'cbum' ? 'Chris Bumstead' : 'Scientific PPL',
    tags: [
      `${frequency}-day`,
      'push_pull_legs',
      goal,
      level,
      phase,
      variant
    ],
    split: 'push_pull_legs'
  };

  return routine;
}

/**
 * Generate a name for the PPL routine
 */
function generateRoutineName(frequency: PplFrequency, variant: PplVariant, phase: PplPhase): string {
  const variantName = variant === 'nippard' ? 'Jeff Nippard' :
                     variant === 'cbum' ? 'Chris Bumstead' :
                     variant === 'volume_focus' ? 'Volume-Focused' :
                     variant === 'strength_focus' ? 'Strength-Focused' : 'Standard';

  const phaseName = phase === 'volume' ? 'Volume Phase' :
                   phase === 'intensity' ? 'Intensity Phase' :
                   phase === 'strength' ? 'Strength Phase' : 'Deload';

  return `${frequency}-Day PPL: ${variantName} (${phaseName})`;
}

/**
 * Generate a description for the PPL routine
 */
function generateRoutineDescription(
  frequency: PplFrequency,
  variant: PplVariant,
  level: TrainingLevel,
  goal: TrainingGoal,
  phase: PplPhase
): string {
  let description = `${frequency}-day Push/Pull/Legs split `;

  // Add variant-specific description
  if (variant === 'nippard') {
    description += `based on Jeff Nippard's scientific approach to hypertrophy training. `;
  } else if (variant === 'cbum') {
    description += `inspired by Chris Bumstead's championship-winning training methodology. `;
  } else if (variant === 'volume_focus') {
    description += `with emphasis on training volume for maximum muscle growth. `;
  } else if (variant === 'strength_focus') {
    description += `with focus on strength development through progressive overload. `;
  } else {
    description += `designed for balanced development of all major muscle groups. `;
  }

  // Add phase-specific description
  if (phase === 'volume') {
    description += `This volume phase emphasizes higher rep ranges and total work volume to maximize hypertrophy stimulus. `;
  } else if (phase === 'intensity') {
    description += `This intensity phase focuses on heavier weights and lower rep ranges to build strength and density. `;
  } else if (phase === 'strength') {
    description += `This strength phase prioritizes compound movements and progressive overload for maximal strength gains. `;
  } else if (phase === 'deload') {
    description += `This deload phase reduces volume and/or intensity to facilitate recovery and supercompensation. `;
  }

  // Add level-specific description
  description += `Designed for ${level} trainees with a primary goal of ${goal}.`;

  return description;
}

/**
 * Generate workout days based on frequency
 * This is a simplified version for the fix
 */
function generateWorkoutDays(
  frequency: PplFrequency,
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[]
): WorkoutDay[] {
  // This is a placeholder - the full implementation would be added in the complete file
  const workoutDays: WorkoutDay[] = [];

  // Create a simple workout day as a placeholder
  const day: WorkoutDay = {
    id: uuidv4(),
    name: "Push Day",
    description: "Chest, shoulders, and triceps training",
    targetMuscleGroups: ['chest', 'shoulders', 'triceps'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };

  workoutDays.push(day);

  return workoutDays;
}

// Placeholder for createPushDay function
function createPushDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[],
  dayNumber: number
): WorkoutDay {
  return {
    id: uuidv4(),
    name: `Push Day ${dayNumber}`,
    description: "Chest, shoulders, and triceps training",
    targetMuscleGroups: ['chest', 'shoulders', 'triceps'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}

// Placeholder for createPullDay function
function createPullDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[],
  dayNumber: number
): WorkoutDay {
  return {
    id: uuidv4(),
    name: `Pull Day ${dayNumber}`,
    description: "Back and biceps training",
    targetMuscleGroups: ['back', 'biceps'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}

// Placeholder for createLegDay function
function createLegDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[],
  dayNumber: number
): WorkoutDay {
  return {
    id: uuidv4(),
    name: `Leg Day ${dayNumber}`,
    description: "Quadriceps, hamstrings, and calves training",
    targetMuscleGroups: ['quadriceps', 'hamstrings', 'calves'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}

// Placeholder for createUpperDay function
function createUpperDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[]
): WorkoutDay {
  return {
    id: uuidv4(),
    name: "Upper Body Day",
    description: "Chest, back, shoulders, and arms training",
    targetMuscleGroups: ['chest', 'back', 'shoulders', 'biceps', 'triceps'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}

// Placeholder for createLowerDay function
function createLowerDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[]
): WorkoutDay {
  return {
    id: uuidv4(),
    name: "Lower Body Day",
    description: "Quadriceps, hamstrings, glutes, and calves training",
    targetMuscleGroups: ['quadriceps', 'hamstrings', 'glutes', 'calves'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}

// Placeholder for createSpecializationDay function
function createSpecializationDay(
  variant: PplVariant,
  level: TrainingLevel,
  phase: PplPhase,
  priorityMuscleGroups: string[],
  equipment: string[]
): WorkoutDay {
  return {
    id: uuidv4(),
    name: "Specialization Day",
    description: "Focus on priority muscle groups",
    targetMuscleGroups: priorityMuscleGroups.length > 0 ? priorityMuscleGroups : ['chest', 'back', 'shoulders'],
    difficulty: level,
    estimatedDuration: 60,
    exerciseSets: []
  };
}
