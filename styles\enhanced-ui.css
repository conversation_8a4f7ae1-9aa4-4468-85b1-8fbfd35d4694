/* Enhanced UI Styles for Routinize Fitness App */

/* Global Variables */
:root {
  /* Color Palette */
  --primary-blue: #1B237E;
  --primary-orange: #FEA800;
  --accent-red: #FF6767;
  --accent-purple: #573353;
  --light-purple: #B1AFE9;
  --lightest-purple: #DDDCFE;
  --background-cream: #FFF3E9;
  
  /* Typography */
  --font-primary: '<PERSON><PERSON><PERSON>', 'Man<PERSON><PERSON>', sans-serif;
  --font-secondary: 'Manrop<PERSON>', sans-serif;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--background-cream);
  color: var(--accent-purple);
  line-height: 1.6;
}

/* Enhanced Card Styles */
.enhanced-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.enhanced-card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-card-content {
  padding: var(--spacing-lg);
}

/* Enhanced Button Styles */
.enhanced-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.enhanced-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.enhanced-button-primary {
  background-color: var(--primary-blue);
  color: white;
}

.enhanced-button-primary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--primary-blue) 90%, black);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.enhanced-button-secondary {
  background-color: var(--primary-orange);
  color: white;
}

.enhanced-button-secondary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--primary-orange) 90%, black);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.enhanced-button-outline {
  background-color: transparent;
  border: 1px solid var(--primary-blue);
  color: var(--primary-blue);
}

.enhanced-button-outline:hover:not(:disabled) {
  background-color: var(--primary-blue);
  color: white;
}

.enhanced-button-ghost {
  background-color: transparent;
  color: var(--accent-purple);
}

.enhanced-button-ghost:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Enhanced Input Styles */
.enhanced-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background-color: white;
}

.enhanced-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(27, 35, 126, 0.1);
}

.enhanced-input:invalid {
  border-color: var(--accent-red);
}

/* Enhanced Badge Styles */
.enhanced-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid transparent;
}

.enhanced-badge-primary {
  background-color: rgba(27, 35, 126, 0.1);
  color: var(--primary-blue);
  border-color: rgba(27, 35, 126, 0.2);
}

.enhanced-badge-success {
  background-color: rgba(34, 197, 94, 0.1);
  color: rgb(22, 163, 74);
  border-color: rgba(34, 197, 94, 0.2);
}

.enhanced-badge-warning {
  background-color: rgba(254, 168, 0, 0.1);
  color: rgb(217, 119, 6);
  border-color: rgba(254, 168, 0, 0.2);
}

.enhanced-badge-danger {
  background-color: rgba(255, 103, 103, 0.1);
  color: rgb(220, 38, 38);
  border-color: rgba(255, 103, 103, 0.2);
}

/* Enhanced Progress Bar */
.enhanced-progress {
  width: 100%;
  height: 0.5rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.enhanced-progress-bar {
  height: 100%;
  background-color: var(--primary-blue);
  transition: width 0.3s ease;
  border-radius: var(--radius-sm);
}

/* Enhanced Navigation */
.enhanced-nav {
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: var(--shadow-sm);
}

.enhanced-nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  color: var(--accent-purple);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: var(--radius-md);
}

.enhanced-nav-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.enhanced-nav-item.active {
  background-color: rgba(27, 35, 126, 0.1);
  color: var(--primary-blue);
  font-weight: 500;
}

/* Enhanced Grid System */
.enhanced-grid {
  display: grid;
  gap: var(--spacing-md);
}

.enhanced-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.enhanced-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.enhanced-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.enhanced-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Design */
@media (min-width: 768px) {
  .enhanced-grid-md-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .enhanced-grid-md-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .enhanced-grid-md-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .enhanced-grid-md-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .enhanced-grid-lg-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .enhanced-grid-lg-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .enhanced-grid-lg-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .enhanced-grid-lg-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

/* Enhanced Typography */
.enhanced-heading-1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--accent-purple);
}

.enhanced-heading-2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--accent-purple);
}

.enhanced-heading-3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--accent-purple);
}

.enhanced-text-body {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--accent-purple);
}

.enhanced-text-small {
  font-size: 0.875rem;
  line-height: 1.5;
  color: rgba(87, 51, 83, 0.7);
}

.enhanced-text-muted {
  color: rgba(87, 51, 83, 0.6);
}

/* Enhanced Animations */
@keyframes enhanced-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes enhanced-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.enhanced-animate-fade-in {
  animation: enhanced-fade-in 0.3s ease-out;
}

.enhanced-animate-slide-in {
  animation: enhanced-slide-in 0.3s ease-out;
}

/* Enhanced Loading States */
.enhanced-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: enhanced-skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes enhanced-skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Focus States */
.enhanced-focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(27, 35, 126, 0.2);
}

/* Enhanced Accessibility */
.enhanced-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 767px) {
  .enhanced-mobile-full-width {
    width: 100%;
  }
  
  .enhanced-mobile-padding {
    padding: var(--spacing-md);
  }
  
  .enhanced-mobile-text-center {
    text-align: center;
  }
}
